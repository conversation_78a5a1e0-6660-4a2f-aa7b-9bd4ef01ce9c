"use client";

import { IBlogPost } from "@/types/blog";
import { BlogCard } from "@/components/blog/card";

export default function BlogPageClient({ initialPosts }: { initialPosts: IBlogPost[] }) {
    const posts = initialPosts ?? [];

    return (
        <div className="container mx-auto px-4 pt-20">
            <div className="grid gap-8 sm:grid-cols-2 lg:grid-cols-3">
                {posts.map((post) => (
                    <BlogCard key={post.id} post={post} />
                ))}
            </div>
        </div>
    );
}
