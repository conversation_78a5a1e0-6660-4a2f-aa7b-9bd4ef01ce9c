export interface ICategory {
    id: string;
    name: string;
    slug: string;
    showOnNav: boolean;
    image: string;
    categories?: ISubCategory[];
    createdAt?: string;
    updatedAt?: string;
}

export interface Category {
    id: string;
    name: string;
    slug: string;
    createdAt?: string;
    updatedAt?: string;
}


export interface ISubCategory {
    id: string;
    name: string;
    slug: string;
    showOnNav: boolean;
    categoryId: string;
    category?: Category[];
    createdAt?: string;
    updatedAt?: string;
}

export interface IProduct {
    id: string;
    name: string;
    slug: string;
    image: string;
    showOnNav: boolean;
    description: string;
    subCategoryId: string;
    createdAt?: string;
    updatedAt?: string;
    subCategory?: ISubCategory;
    tabs?: IProductTab[];
}

export interface IProductTab {
    id: string;
    name: string;
    productId: string;
    items?: IProductTabItem[];
    createdAt?: string;
    updatedAt?: string;
}

export interface IProductTabItem {
    id: string;
    title: string;
    description: string;
    productTabId: string;
    createdAt?: string;
    updatedAt?: string;
}
