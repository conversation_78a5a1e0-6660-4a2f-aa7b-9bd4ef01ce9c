"use client"

import Image from "next/image"
import Link from "next/link"
import {
    <PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON>,
    Card<PERSON><PERSON>le,
} from "@/components/ui/card"
import type { FC } from "react"
import { IBlogPost } from "@/types/blog"
import { ArrowRight } from "lucide-react"

interface BlogCardProps {
    post: IBlogPost
}

export const BlogCard: FC<BlogCardProps> = ({ post }) => {
    const baseImageUrl = process.env.NEXT_PUBLIC_IMAGE_URL ?? "";
    const imgUrl = post.image
        ? (baseImageUrl ? `${baseImageUrl}/${post.image}` : post.image)
        : "/images/helicopter.png";


    // const excerpt =
    //     post.content.replace(/<[^>]+>/g, "").slice(0, 120).trimEnd() + "..."

    return (
        <div className="group relative overflow-hidden transition-shadow p-0 mb-10">
            {/* image container with fill + object-cover */}
            <div className="relative w-full h-56">
                <Image
                    src={imgUrl}
                    alt={post.title}
                    fill
                    className="object-cover transition-transform duration-500 group-hover:scale-105"
                />
            </div>

            <div className="flex-1 flex flex-col mt-2">
                <p className="text-sm text-muted-foreground leading-relaxed">
                    {new Date(post.createdAt).toLocaleDateString()}
                </p>
                <CardHeader className="pt-0 p-0">
                    <CardTitle className="text-xl font-bold mb-2">{post.title}</CardTitle>
                </CardHeader>

                <CardContent className="p-0 mb-4 flex-1">
                    <p className="text-sm leading-relaxed line-clamp-3 ">
                        {post.content}
                    </p>
                </CardContent>

                <Link href={`/news/${post.slug}`} passHref>
                    <p className="text-sm text-primary underline-offset-4 hover:underline">
                        Read More <ArrowRight className="h-4 w-4 inline-block" />
                    </p>
                </Link>
            </div>
        </div>
    )
}
